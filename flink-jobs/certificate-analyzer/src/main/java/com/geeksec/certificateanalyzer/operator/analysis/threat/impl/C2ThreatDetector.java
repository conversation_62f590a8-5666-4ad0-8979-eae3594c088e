package com.geeksec.certificateanalyzer.operator.analysis.detector.impl;

import java.util.List;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.detector.BaseCertificateDetector;
import com.geeksec.common.knowledge.KnowledgeBaseClient;

import lombok.extern.slf4j.Slf4j;

/**
 * C&C威胁检测器
 * 使用懒加载模式，通过KnowledgeBaseClient的缓存机制检测证书中的域名和IP是否在C2威胁情报列表中
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class C2ThreatDetector extends BaseCertificateDetector {

    /** 知识库客户端 */
    private final KnowledgeBaseClient knowledgeBaseClient;

    public C2ThreatDetector() {
        super("C2 Threat Detector");
        this.knowledgeBaseClient = new KnowledgeBaseClient();
        log.info("C2威胁检测器初始化完成，使用懒加载模式");
    }
    
    @Override
    protected void doDetect(X509Certificate cert) {
        if (cert == null) {
            return;
        }

        List<String> domains = cert.getCertDomains();
        List<String> ips = cert.getCertIps();

        // 懒加载检查域名 - 每个域名单独查询，利用KnowledgeBaseClient的缓存
        for (String domain : domains) {
            if (knowledgeBaseClient.isC2ThreatDomain(domain)) {
                cert.getLabels().add(CertificateLabel.CC_CERT);
                log.debug("检测到C2威胁域名: {}", domain);
                return;
            }
        }

        // 懒加载检查IP - 每个IP单独查询，利用KnowledgeBaseClient的缓存
        for (String ip : ips) {
            if (knowledgeBaseClient.isC2ThreatIp(ip)) {
                cert.getLabels().add(CertificateLabel.CC_CERT);
                log.debug("检测到C2威胁IP: {}", ip);
                return;
            }
        }
    }
}
