package com.geeksec.certificateanalyzer.operator.analysis.attribute;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.validator.routines.EmailValidator;
import org.apache.flink.api.common.functions.MapFunction;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书属性提取器
 * 
 * 职责：
 * 1. 提取证书的基础属性和元数据信息
 * 2. 不涉及安全判断，仅进行纯数据提取
 * 3. 为后续的合规验证、信任评估和威胁检测提供基础数据
 * 
 * 提取的属性包括：
 * - 域名信息
 * - 证书字段信息（Subject、Issuer等）
 * - 时间属性（有效期等）
 * - 加密算法信息
 * - 颁发者信息
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificateAttributeExtractor implements MapFunction<X509Certificate, X509Certificate> {

    @Override
    public X509Certificate map(X509Certificate certificate) throws Exception {
        log.debug("执行证书属性提取，证书ID: {}", certificate.getDerSha1());

        // 提取域名信息
        extractDomainAttributes(certificate);

        // 提取证书字段信息
        extractCertificateFields(certificate);

        // 提取时间属性
        extractTemporalAttributes(certificate);

        // 提取加密算法信息
        extractCryptographicAttributes(certificate);

        // 提取颁发者信息
        extractIssuerAttributes(certificate);

        // 提取SAN值信息
        extractSanAttributes(certificate);

        // 提取证书扩展信息
        extractExtensionAttributes(certificate);

        return certificate;
    }

    /**
     * 提取域名信息
     * 纯属性提取，不进行任何安全判断
     */
    private void extractDomainAttributes(X509Certificate certificate) {
        List<String> domains = certificate.getCertDomains();
        
        if (domains != null && !domains.isEmpty()) {
            log.debug("证书包含 {} 个域名", domains.size());
            
            // 这里可以添加域名属性的统计信息提取
            // 例如：域名数量、通配符域名数量、国际化域名数量等
            // 但不进行安全相关的判断
        }
    }

    /**
     * 提取证书字段信息
     * 提取Subject、Issuer等字段的基础信息
     */
    private void extractCertificateFields(X509Certificate certificate) {
        // 提取Subject字段信息
        Map<String, String> subject = certificate.getSubject();
        if (subject != null) {
            log.debug("Subject字段包含 {} 个属性", subject.size());
            // 可以添加Subject字段的统计信息
        }

        // 提取Issuer字段信息
        Map<String, String> issuer = certificate.getIssuer();
        if (issuer != null) {
            log.debug("Issuer字段包含 {} 个属性", issuer.size());
            // 可以添加Issuer字段的统计信息
        }
    }

    /**
     * 提取时间属性
     * 提取证书的时间相关属性
     */
    private void extractTemporalAttributes(X509Certificate certificate) {
        LocalDateTime notBefore = certificate.getNotBefore();
        LocalDateTime notAfter = certificate.getNotAfter();

        if (notBefore != null && notAfter != null) {
            Duration validityPeriod = Duration.between(notBefore, notAfter);
            long validityDays = validityPeriod.toDays();
            
            log.debug("证书有效期: {} 天", validityDays);
            
            // 可以将有效期天数等信息存储到证书对象中
            // 为后续的合规性检查提供数据支持
        }
    }

    /**
     * 提取加密算法信息
     * 提取证书使用的加密算法相关信息
     */
    private void extractCryptographicAttributes(X509Certificate certificate) {
        String signatureAlgorithm = certificate.getSignatureAlgorithm();
        
        if (signatureAlgorithm != null) {
            log.debug("签名算法: {}", signatureAlgorithm);
            // 可以添加算法类型的分类信息提取
        }
        
        // 可以提取其他加密相关属性
        String publicKeyAlgorithm = certificate.getPublicKeyAlgorithm();
        if (publicKeyAlgorithm != null) {
            log.debug("公钥算法: {}", publicKeyAlgorithm);
        }
    }

    /**
     * 提取颁发者信息
     * 提取证书颁发者的相关信息
     */
    private void extractIssuerAttributes(X509Certificate certificate) {
        Map<String, String> issuer = certificate.getIssuer();
        
        if (issuer != null) {
            String issuerOrg = issuer.get("O");
            String issuerCN = issuer.get("CN");
            
            if (issuerOrg != null) {
                log.debug("颁发机构: {}", issuerOrg);
            }
            
            if (issuerCN != null) {
                log.debug("颁发者CN: {}", issuerCN);
            }
        }
    }

    /**
     * 提取SAN值信息
     * 提取Subject Alternative Names的基础信息
     */
    private void extractSanAttributes(X509Certificate certificate) {
        List<String> sanList = certificate.getSubjectAltNames();
        if (sanList == null || sanList.isEmpty()) {
            log.debug("证书不包含SAN值");
            return;
        }

        log.debug("证书包含 {} 个SAN值", sanList.size());

        // 统计不同类型的SAN值数量
        int ipCount = 0;
        int wildcardCount = 0;
        int emailCount = 0;
        int uriCount = 0;

        for (String san : sanList) {
            // 检查IP地址
            if (isValidIp(san)) {
                ipCount++;
            }

            // 检查通配符
            if (san.startsWith("*.")) {
                wildcardCount++;
            }

            // 检查邮箱格式
            if (EmailValidator.getInstance().isValid(san)) {
                emailCount++;
            }

            // 检查URI格式
            if (san.startsWith("http://") || san.startsWith("https://") || san.startsWith("ftp://")) {
                uriCount++;
            }
        }

        log.debug("SAN值统计 - IP: {}, 通配符: {}, 邮箱: {}, URI: {}",
            ipCount, wildcardCount, emailCount, uriCount);
    }

    /**
     * 提取证书扩展信息
     * 提取证书扩展的基础信息
     */
    private void extractExtensionAttributes(X509Certificate certificate) {
        if (certificate.getCertificateExtensions() != null &&
                certificate.getCertificateExtensions().getExtensionMap() != null) {

            Map<String, Object> extensionMap = certificate.getCertificateExtensions().getExtensionMap();
            log.debug("证书包含 {} 个扩展", extensionMap.size());

            // 记录关键扩展的存在性
            String[] keyExtensions = {
                "basicConstraints", "keyUsage", "extendedKeyUsage",
                "subjectAltName", "certificatePolicies", "authorityInfoAccess"
            };

            for (String extension : keyExtensions) {
                if (extensionMap.containsKey(extension)) {
                    log.debug("发现扩展: {}", extension);
                }
            }
        } else {
            log.debug("证书不包含扩展信息");
        }
    }

    /**
     * 简单的IP地址验证
     * 这里使用简化的验证逻辑，实际可以使用更完善的IP验证工具
     */
    private boolean isValidIp(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }

        // IPv4简单验证
        String[] parts = ip.split("\\.");
        if (parts.length == 4) {
            try {
                for (String part : parts) {
                    int num = Integer.parseInt(part);
                    if (num < 0 || num > 255) {
                        return false;
                    }
                }
                return true;
            } catch (NumberFormatException e) {
                return false;
            }
        }

        // IPv6简单验证（包含冒号）
        return ip.contains(":");
    }
}
