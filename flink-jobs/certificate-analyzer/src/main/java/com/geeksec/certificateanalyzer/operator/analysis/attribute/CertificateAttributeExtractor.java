package com.geeksec.certificateanalyzer.operator.analysis.attribute;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import org.apache.flink.api.common.functions.MapFunction;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书属性提取器
 * 
 * 职责：
 * 1. 提取证书的基础属性和元数据信息
 * 2. 不涉及安全判断，仅进行纯数据提取
 * 3. 为后续的合规验证、信任评估和威胁检测提供基础数据
 * 
 * 提取的属性包括：
 * - 域名信息
 * - 证书字段信息（Subject、Issuer等）
 * - 时间属性（有效期等）
 * - 加密算法信息
 * - 颁发者信息
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificateAttributeExtractor implements MapFunction<X509Certificate, X509Certificate> {

    @Override
    public X509Certificate map(X509Certificate certificate) throws Exception {
        log.debug("执行证书属性提取，证书ID: {}", certificate.getDerSha1());

        // 提取域名信息
        extractDomainAttributes(certificate);

        // 提取证书字段信息
        extractCertificateFields(certificate);

        // 提取时间属性
        extractTemporalAttributes(certificate);

        // 提取加密算法信息
        extractCryptographicAttributes(certificate);

        // 提取颁发者信息
        extractIssuerAttributes(certificate);

        return certificate;
    }

    /**
     * 提取域名信息
     * 纯属性提取，不进行任何安全判断
     */
    private void extractDomainAttributes(X509Certificate certificate) {
        List<String> domains = certificate.getCertDomains();
        
        if (domains != null && !domains.isEmpty()) {
            log.debug("证书包含 {} 个域名", domains.size());
            
            // 这里可以添加域名属性的统计信息提取
            // 例如：域名数量、通配符域名数量、国际化域名数量等
            // 但不进行安全相关的判断
        }
    }

    /**
     * 提取证书字段信息
     * 提取Subject、Issuer等字段的基础信息
     */
    private void extractCertificateFields(X509Certificate certificate) {
        // 提取Subject字段信息
        Map<String, String> subject = certificate.getSubject();
        if (subject != null) {
            log.debug("Subject字段包含 {} 个属性", subject.size());
            // 可以添加Subject字段的统计信息
        }

        // 提取Issuer字段信息
        Map<String, String> issuer = certificate.getIssuer();
        if (issuer != null) {
            log.debug("Issuer字段包含 {} 个属性", issuer.size());
            // 可以添加Issuer字段的统计信息
        }
    }

    /**
     * 提取时间属性
     * 提取证书的时间相关属性
     */
    private void extractTemporalAttributes(X509Certificate certificate) {
        LocalDateTime notBefore = certificate.getNotBefore();
        LocalDateTime notAfter = certificate.getNotAfter();

        if (notBefore != null && notAfter != null) {
            Duration validityPeriod = Duration.between(notBefore, notAfter);
            long validityDays = validityPeriod.toDays();
            
            log.debug("证书有效期: {} 天", validityDays);
            
            // 可以将有效期天数等信息存储到证书对象中
            // 为后续的合规性检查提供数据支持
        }
    }

    /**
     * 提取加密算法信息
     * 提取证书使用的加密算法相关信息
     */
    private void extractCryptographicAttributes(X509Certificate certificate) {
        String signatureAlgorithm = certificate.getSignatureAlgorithm();
        
        if (signatureAlgorithm != null) {
            log.debug("签名算法: {}", signatureAlgorithm);
            // 可以添加算法类型的分类信息提取
        }
        
        // 可以提取其他加密相关属性
        String publicKeyAlgorithm = certificate.getPublicKeyAlgorithm();
        if (publicKeyAlgorithm != null) {
            log.debug("公钥算法: {}", publicKeyAlgorithm);
        }
    }

    /**
     * 提取颁发者信息
     * 提取证书颁发者的相关信息
     */
    private void extractIssuerAttributes(X509Certificate certificate) {
        Map<String, String> issuer = certificate.getIssuer();
        
        if (issuer != null) {
            String issuerOrg = issuer.get("O");
            String issuerCN = issuer.get("CN");
            
            if (issuerOrg != null) {
                log.debug("颁发机构: {}", issuerOrg);
            }
            
            if (issuerCN != null) {
                log.debug("颁发者CN: {}", issuerCN);
            }
        }
    }
}
