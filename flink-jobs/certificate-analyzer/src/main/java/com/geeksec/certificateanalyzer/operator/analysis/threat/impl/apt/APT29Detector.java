package com.geeksec.certificateanalyzer.operator.analysis.detector.impl.apt;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.detector.BaseCertificateDetector;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * APT29 威胁组织证书检测器
 * <p>
 * 检测 APT29 威胁组织使用的证书特征：
 * - 丢失的证书列表 (LOST_CERT_LIST)
 * - 特殊密钥ID (SPECIAL_KEY_ID)
 * - SAN中包含IP地址 (IP_IN_SAN)
 * - 颁发者中包含通配符 (WILDCARD_IN_ISSUER)
 * - 长期有效的证书 (LONG_VALIDITY_CERT)
 *
 * 检测逻辑：
 * 当证书同时满足以上五个条件时，判定为 APT29 威胁证书
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class APT29Detector extends BaseCertificateDetector {
    public static final String DETECTOR_NAME = "APT29 Detector";
    
    public APT29Detector() {
        super(DETECTOR_NAME);
    }
    
    @Override
    protected void doDetect(X509Certificate cert) {
        if (cert == null) {
            return;
        }

        Set<CertificateLabel> labels = cert.getLabels();
        if (isApt29Certificate(cert)) {
            labels.add(CertificateLabel.APT29_CERT);
            log.debug("检测到潜在的 APT29 威胁证书: {}", cert.getCommonName());
        }
    }

    /**
     * 检查证书是否符合APT29的特征
     * <p>
     * 检测条件：
     * 1. 丢失的证书列表 (LOST_CERT_LIST)
     * 2. 特殊密钥ID (SPECIAL_KEY_ID)
     * 3. SAN中包含IP地址 (IP_IN_SAN)
     * 4. 颁发者中包含通配符 (WILDCARD_IN_ISSUER)
     * 5. 长期有效的证书 (LONG_VALIDITY_CERT)
     *
     * @param cert 证书对象
     * @return 如果符合 APT29 特征返回 true，否则返回 false
     */
    private boolean isApt29Certificate(X509Certificate cert) {
        Set<CertificateLabel> labels = cert.getLabels();

        boolean result = labels.contains(CertificateLabel.LOST_CERT_LIST)
            && labels.contains(CertificateLabel.SPECIAL_KEY_ID)
            && labels.contains(CertificateLabel.IP_IN_SAN)
            && labels.contains(CertificateLabel.WILDCARD_IN_ISSUER)
            && labels.contains(CertificateLabel.LONG_VALIDITY_CERT);

        if (result) {
            log.debug("APT29 检测条件满足: 丢失证书列表 + 特殊密钥ID + SAN中IP + 颁发者通配符 + 长期证书");
        }

        return result;
    }
}
