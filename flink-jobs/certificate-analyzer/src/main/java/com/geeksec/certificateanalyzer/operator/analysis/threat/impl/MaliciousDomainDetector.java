package com.geeksec.certificateanalyzer.operator.analysis.detector.impl;

import java.util.List;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.detector.BaseCertificateDetector;
import com.geeksec.common.knowledge.KnowledgeBaseClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 恶意域名检测器
 * 使用懒加载模式，通过KnowledgeBaseClient的缓存机制检测证书中的域名和IP是否在恶意域名、IOC IP和挖矿域名列表中
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class MaliciousDomainDetector extends BaseCertificateDetector {

    /** 知识库客户端 */
    private final KnowledgeBaseClient knowledgeBaseClient;

    public MaliciousDomainDetector() {
        super("Malicious Domain Detector");
        this.knowledgeBaseClient = new KnowledgeBaseClient();
        log.info("恶意域名检测器初始化完成，使用懒加载模式");
    }
    
    @Override
    protected void doDetect(X509Certificate cert) {
        if (cert == null) {
            return;
        }

        List<String> domains = cert.getCertDomains();
        List<String> ips = cert.getCertIps();

        // 懒加载检测恶意域名 - 每个域名单独查询，利用KnowledgeBaseClient的缓存
        for (String domain : domains) {
            if (knowledgeBaseClient.isMaliciousDomain(domain)) {
                cert.getLabels().add(CertificateLabel.MALICIOUS_DOMAIN_CERT);
                log.debug("检测到恶意域名: {}", domain);
                break;
            }
        }

        // 懒加载检测IOC IP - 每个IP单独查询，利用KnowledgeBaseClient的缓存
        for (String ip : ips) {
            if (knowledgeBaseClient.isIocIp(ip)) {
                cert.getLabels().add(CertificateLabel.IOC_IP_CERT);
                log.debug("检测到IOC IP: {}", ip);
                break;
            }
        }

        // 懒加载检测挖矿证书 - 每个域名单独查询，利用KnowledgeBaseClient的缓存
        for (String domain : domains) {
            if (knowledgeBaseClient.isMiningDomain(domain)) {
                cert.getLabels().add(CertificateLabel.MINING_CERT);
                log.debug("检测到挖矿域名: {}", domain);
                break;
            }
        }
    }
}
