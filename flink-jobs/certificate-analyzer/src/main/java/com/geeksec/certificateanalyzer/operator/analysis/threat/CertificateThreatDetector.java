package com.geeksec.certificateanalyzer.operator.analysis.threat;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.threat.impl.C2ThreatDetector;
import com.geeksec.certificateanalyzer.operator.analysis.threat.impl.DistributedServicesDetector;
import com.geeksec.certificateanalyzer.operator.analysis.threat.impl.MaliciousDomainDetector;
import com.geeksec.certificateanalyzer.operator.analysis.threat.impl.MiningThreatDetector;
import com.geeksec.certificateanalyzer.operator.analysis.threat.impl.OpenSSLSelfSignDetector;
import com.geeksec.certificateanalyzer.operator.analysis.threat.impl.ThreatIntelligenceDetector;
import com.geeksec.certificateanalyzer.operator.analysis.threat.impl.TorDetector;
import com.geeksec.certificateanalyzer.operator.analysis.threat.impl.TyposquattingDetector;
import com.geeksec.certificateanalyzer.operator.analysis.threat.impl.apt.APT28Detector;
import com.geeksec.certificateanalyzer.operator.analysis.threat.impl.apt.APT29Detector;
import com.geeksec.certificateanalyzer.operator.analysis.threat.impl.apt.PatchWorkDetector;
import com.geeksec.certificateanalyzer.operator.analysis.threat.impl.botnet.DanaBotDetector;
import com.geeksec.certificateanalyzer.operator.analysis.threat.impl.botnet.QuakbotDetector;
import com.geeksec.certificateanalyzer.operator.analysis.threat.impl.botnet.StealcDetector;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书威胁检测器
 * 
 * 职责：
 * 1. 检测与证书相关的各种威胁和恶意活动
 * 2. 管理和协调所有威胁检测器的执行
 * 3. 处理检测器异常和错误隔离
 * 4. 提供统一的威胁检测入口点
 * 
 * 检测内容包括：
 * - 恶意域名检测
 * - DGA域名检测
 * - 域名仿冒检测
 * - APT威胁检测
 * - Botnet威胁检测
 * - C2威胁检测
 * - 可疑行为检测
 * 
 * 支持的威胁检测器：
 * - APT威胁检测器（APT28、APT29、PatchWork等）
 * - Botnet威胁检测器（DanaBot、Quakbot、Stealc等）
 * - Tor网络检测器
 * - C2威胁检测器
 * - 恶意域名检测器
 * - 域名仿冒检测器
 * - 挖矿威胁检测器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificateThreatDetector extends RichMapFunction<X509Certificate, X509Certificate> {

    /** 所有威胁检测器列表 */
    private List<CertificateDetector> detectors;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 初始化所有威胁检测器
        initializeDetectors();

        log.info("证书威胁检测器初始化完成，共加载 {} 个检测器", detectors.size());
    }

    @Override
    public X509Certificate map(X509Certificate certificate) throws Exception {
        log.debug("执行证书威胁检测，证书ID: {}", certificate.getDerSha1());

        Set<CertificateLabel> labels = certificate.getLabels();
        if (labels == null) {
            labels = new HashSet<>();
        }

        // 执行基础威胁检测逻辑（从原CertificateFeatureAnalyzer迁移）
        detectBasicThreats(certificate, labels);

        // 依次调用所有威胁检测器进行高级威胁检测
        for (CertificateDetector detector : detectors) {
            try {
                detector.detect(certificate);
            } catch (Exception e) {
                log.error("检测器 {} 执行失败: {}", detector.getName(), e.getMessage(), e);
                // 继续执行其他检测器，不因单个检测器失败而中断整个流程
            }
        }

        certificate.setLabels(labels);
        return certificate;
    }

    /**
     * 初始化所有威胁检测器
     */
    private void initializeDetectors() {
        detectors = Arrays.asList(
            // Botnet检测器
            new DanaBotDetector(),
            new StealcDetector(),
            new QuakbotDetector(),

            // APT检测器
            new APT28Detector(),
            new APT29Detector(),
            new PatchWorkDetector(),

            // 网络渗透检测器
            new TorDetector(),

            // 威胁情报检测器
            new C2ThreatDetector(),
            new MaliciousDomainDetector(),
            new ThreatIntelligenceDetector(),
            new MiningThreatDetector(),

            // 域名相关检测器
            new TyposquattingDetector(),
            new DistributedServicesDetector(),

            // 特殊签名检测器
            new OpenSSLSelfSignDetector()
        );

        log.info("初始化威胁检测器: {}",
            detectors.stream().map(CertificateDetector::getName).toArray());
    }

    /**
     * 执行基础威胁检测逻辑
     * 这些逻辑从原CertificateFeatureAnalyzer和CertificateDetailAnalyzer迁移而来
     */
    private void detectBasicThreats(X509Certificate certificate, Set<CertificateLabel> labels) {
        // 域名威胁检测
        detectDomainThreats(certificate, labels);

        // 可疑行为检测
        detectSuspiciousBehaviors(certificate, labels);

        // 颁发者威胁检测
        detectIssuerThreats(certificate, labels);

        // 域名特征威胁检测
        detectDomainFeatureThreats(certificate, labels);
    }

    /**
     * 域名威胁检测
     * 从原CertificateFeatureAnalyzer的analyzeDomainFeatures方法迁移
     */
    private void detectDomainThreats(X509Certificate certificate, Set<CertificateLabel> labels) {
        List<String> domains = certificate.getCertDomains();

        for (String domain : domains) {
            // 检测域名生成算法(DGA)
            if (isDGADomain(domain)) {
                labels.add(CertificateLabel.DGA_DOMAIN_CERT);
                log.debug("检测到DGA域名: {}", domain);
            }

            // 检测域名抢注
            if (isTyposquattingDomain(domain)) {
                labels.add(CertificateLabel.TYPOSQUATTING);
                log.debug("检测到域名抢注: {}", domain);
            }

            // 检测最近注册的域名
            if (isRecentlyRegisteredDomain(domain)) {
                labels.add(CertificateLabel.RECENTLY_REGISTERED);
                log.debug("检测到最近注册域名: {}", domain);
            }
        }
    }

    /**
     * 可疑行为检测
     * 从原CertificateFeatureAnalyzer的analyzeCertificateFields方法迁移
     */
    private void detectSuspiciousBehaviors(X509Certificate certificate, Set<CertificateLabel> labels) {
        // 分析Subject字段
        var subject = certificate.getSubject();
        if (subject != null) {
            String organization = subject.get("O");
            if (organization != null && containsSuspiciousOrganization(organization)) {
                labels.add(CertificateLabel.SUSPICIOUS_ORGANIZATION);
                log.debug("检测到可疑组织: {}", organization);
            }
        }

        // 分析Issuer字段
        var issuer = certificate.getIssuer();
        if (issuer != null) {
            String issuerOrg = issuer.get("O");
            if (issuerOrg != null && containsSuspiciousIssuer(issuerOrg)) {
                labels.add(CertificateLabel.SUSPICIOUS_ISSUER);
                log.debug("检测到可疑颁发者: {}", issuerOrg);
            }
        }
    }

    /**
     * 颁发者威胁检测
     * 从原CertificateDetailAnalyzer迁移而来
     */
    private void detectIssuerThreats(X509Certificate certificate, Set<CertificateLabel> labels) {
        Map<String, String> issuer = certificate.getIssuer();
        if (issuer == null) {
            return;
        }

        String issuerCn = issuer.get("CN");
        if (issuerCn != null) {
            // 检测颁发者通配符
            if (issuerCn.contains("*")) {
                labels.add(CertificateLabel.SUSPICIOUS_ISSUER);
                log.debug("检测到颁发者通配符: {}", issuerCn);
            }

            // 检测可疑颁发者模式
            if (issuerCn.toLowerCase().contains("fake") ||
                    issuerCn.toLowerCase().contains("test") ||
                    issuerCn.toLowerCase().contains("temp")) {
                labels.add(CertificateLabel.SUSPICIOUS_ISSUER);
                log.debug("检测到可疑颁发者: {}", issuerCn);
            }
        }
    }

    /**
     * 域名特征威胁检测
     * 从原CertificateDetailAnalyzer迁移而来
     */
    private void detectDomainFeatureThreats(X509Certificate certificate, Set<CertificateLabel> labels) {
        List<String> domains = certificate.getCertDomains();
        if (domains == null || domains.isEmpty()) {
            return;
        }

        for (String domain : domains) {
            // 检测热门域名
            if (isPopularDomain(domain)) {
                labels.add(CertificateLabel.POPULAR_DOMAIN);
                log.debug("检测到热门域名: {}", domain);
            }

            // 检测CDN域名
            if (isCdnDomain(domain)) {
                labels.add(CertificateLabel.CDN_DOMAIN);
                log.debug("检测到CDN域名: {}", domain);
            }

            // 检测可疑域名模式
            if (isSuspiciousDomainPattern(domain)) {
                labels.add(CertificateLabel.SUSPICIOUS_DOMAIN_PATTERN);
                log.debug("检测到可疑域名模式: {}", domain);
            }
        }
    }

    /**
     * 威胁检测辅助方法（从原CertificateFeatureAnalyzer迁移）
     */
    private boolean isDGADomain(String domain) {
        return domain.length() > 15 && !domain.contains("-") &&
               domain.chars().filter(ch -> Character.isDigit(ch)).count() > 3;
    }

    private boolean isTyposquattingDomain(String domain) {
        // 简化实现，实际需要与知名域名进行相似度比较
        return false;
    }

    private boolean isRecentlyRegisteredDomain(String domain) {
        // 简化实现，实际需要查询域名注册时间
        return false;
    }

    private boolean containsSuspiciousOrganization(String organization) {
        String[] suspiciousKeywords = {"test", "temp", "fake", "malware", "unknown"};
        String lowerOrg = organization.toLowerCase();
        for (String keyword : suspiciousKeywords) {
            if (lowerOrg.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    private boolean containsSuspiciousIssuer(String issuer) {
        return issuer.toLowerCase().contains("self") ||
               issuer.toLowerCase().contains("unknown") ||
               issuer.toLowerCase().contains("test");
    }

    /**
     * 检测是否为热门域名
     */
    private boolean isPopularDomain(String domain) {
        String[] popularDomains = {
            "google.com", "facebook.com", "amazon.com", "microsoft.com",
            "apple.com", "netflix.com", "twitter.com", "instagram.com",
            "linkedin.com", "youtube.com", "github.com", "stackoverflow.com"
        };

        String lowerDomain = domain.toLowerCase();
        for (String popular : popularDomains) {
            if (lowerDomain.contains(popular)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检测是否为CDN域名
     */
    private boolean isCdnDomain(String domain) {
        String[] cdnPatterns = {
            "cloudflare", "amazonaws", "azureedge", "fastly",
            "cloudfront", "akamai", "maxcdn", "jsdelivr",
            "unpkg", "cdnjs", "bootstrapcdn"
        };

        String lowerDomain = domain.toLowerCase();
        for (String pattern : cdnPatterns) {
            if (lowerDomain.contains(pattern)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检测可疑域名模式
     */
    private boolean isSuspiciousDomainPattern(String domain) {
        String lowerDomain = domain.toLowerCase();

        // 检测可疑关键词
        String[] suspiciousKeywords = {
            "phishing", "malware", "virus", "trojan", "botnet",
            "spam", "scam", "fake", "fraud", "hack"
        };

        for (String keyword : suspiciousKeywords) {
            if (lowerDomain.contains(keyword)) {
                return true;
            }
        }

        // 检测过多数字的域名
        long digitCount = domain.chars().filter(Character::isDigit).count();
        if (digitCount > domain.length() * 0.5) {
            return true;
        }

        return false;
    }

    @Override
    public void close() throws Exception {
        log.debug("证书威胁检测器关闭完成");
        super.close();
    }
}
